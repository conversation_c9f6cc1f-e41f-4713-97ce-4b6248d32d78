// QR Code翻译补充 - 优化版JavaScript注入脚本
// 版本: v1.1
// 测试状态: ✅ 已验证有效

/**
 * 通用翻译添加脚本
 * @param {string} language - 语言代码 (如: 'id', 'ms', 'ja', 'ko', 'th', 'vi', 'ru')
 * @param {string} description - 翻译描述内容
 * @param {string} remark - 翻译备注内容
 * @param {boolean} autoSubmit - 是否自动提交 (默认: false)
 */
function addQRCodeTranslation(language, description, remark, autoSubmit = false) {
    console.log(`🚀 开始添加翻译 - 语言: ${language}`);
    
    // 等待元素加载的辅助函数
    function waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素未找到: ${selector}`));
            }, timeout);
        });
    }
    
    // 步骤1: 选择语言
    try {
        const langSelect = document.getElementById('qrCodeSubTranslate_lang');
        if (langSelect) {
            langSelect.value = language;
            console.log(`✅ 语言选择完成: ${language}`);
        } else {
            throw new Error('语言选择框未找到');
        }
    } catch (error) {
        console.error(`❌ 语言选择失败: ${error.message}`);
        return false;
    }
    
    // 步骤2: 填写Description
    try {
        const descriptionTextarea = document.getElementById('qrCodeSubTranslate_description');
        if (descriptionTextarea) {
            descriptionTextarea.value = description;
            console.log('✅ Description填写完成');
        } else {
            throw new Error('Description输入框未找到');
        }
    } catch (error) {
        console.error(`❌ Description填写失败: ${error.message}`);
        return false;
    }
    
    // 步骤3: 填写Remark
    try {
        const remarkTextarea = document.getElementById('qrCodeSubTranslate_remark');
        if (remarkTextarea) {
            remarkTextarea.value = remark;
            console.log('✅ Remark填写完成');
        } else {
            throw new Error('Remark输入框未找到');
        }
    } catch (error) {
        console.error(`❌ Remark填写失败: ${error.message}`);
        return false;
    }
    
    // 步骤4: 自动提交 (可选)
    if (autoSubmit) {
        try {
            // 精确的Submit按钮选择器 (基于实际HTML结构)
            const submitSelectors = [
                'button.btn.btn-primary[onclick*="submit_qrCodeSubTranslate_form()"]',
                'button.btn.btn-primary:contains("Submit")',
                'button[onclick*="submit_qrCodeSubTranslate_form"]',
                'button.btn-primary'
            ];

            let submitButton = null;
            for (const selector of submitSelectors) {
                if (selector.includes(':contains')) {
                    // 处理包含文本的选择器
                    const buttons = document.querySelectorAll('button.btn.btn-primary');
                    for (const btn of buttons) {
                        if (btn.textContent.trim() === 'Submit') {
                            submitButton = btn;
                            break;
                        }
                    }
                } else {
                    submitButton = document.querySelector(selector);
                }
                if (submitButton) break;
            }

            if (submitButton) {
                console.log('✅ 找到Submit按钮，准备提交...');
                submitButton.click();
                console.log('✅ Submit按钮已点击');
                return true;
            } else {
                console.warn('⚠️ Submit按钮未找到，需要手动提交');
                return false;
            }
        } catch (error) {
            console.error(`❌ 自动提交失败: ${error.message}`);
            return false;
        }
    }
    
    console.log('🎯 翻译内容填写完成');
    return true;
}

// 预定义的翻译数据 - Melaka Private Tour (10 Hours)
const melakaTranslations = {
    'id': {
        description: 'Tur eksklusif 10 jam di Melaka dengan sopir-pemandu (Mandarin/Inggris). Tidak termasuk tiket masuk objek wisata',
        remark: '● Penjemputan: Hanya lokasi tertentu di Kuala Lumpur ● Pemesanan: Sebelum 20:00 sehari sebelumnya ● Waktu: Termasuk perjalanan pulang-pergi ● Makanan: Ditanggung sendiri ● Biaya tambahan: Durasi >10 jam & transfer bandara'
    },
    'ja': {
        description: 'マラッカ10時間プライベートツアー、ドライバー兼ガイド付き（中国語/英語）。入場料別途',
        remark: '● 送迎: クアラルンプール指定場所のみ ● 予約: 出発前日20時まで ● 時間: 往復送迎時間含む ● 食事: 各自負担 ● 追加料金: 10時間超過 & 空港送迎'
    },
    'ko': {
        description: '말라카 10시간 전용투어, 기사 겸 가이드 동행 (중국어/영어). 입장권 불포함',
        remark: '● 픽업: 쿠알라룸푸르 지정 장소만 ● 예약: 출발 전날 오후 8시까지 ● 시간: 왕복 이동시간 포함 ● 식사: 개별 준비 ● 추가요금: 10시간 초과 & 공항 픽업'
    },
    'th': {
        description: 'ทัวร์ส่วนตัว 10 ชั่วโมงที่มะละกา พร้อมคนขับ-ไกด์ (จีน/อังกฤษ). ไม่รวมค่าเข้าชมสถานที่',
        remark: '● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์ ● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า ● เวลา: รวมการเดินทางไป-กลับ ● อาหาร: รับผิดชอบเอง ● ค่าเพิ่ม: เกิน 10 ชม. & รับส่งสนามบิน'
    },
    'vi': {
        description: 'Tour riêng 10 giờ tại Malacca với tài xế-hướng dẫn (Trung/Anh). Không bao gồm vé tham quan',
        remark: '● Đón khách: Chỉ các địa điểm chỉ định tại Kuala Lumpur ● Đặt dịch vụ: Trước 20:00 ngày hôm trước ● Thời gian: Bao gồm di chuyển khứ hồi ● Ăn uống: Khách tự túc ● Phụ phí: Vượt 10 giờ & đưa đón sân bay'
    },
    'ru': {
        description: '10-часовой частный тур по Малакке с водителем-гидом (китайский/английский). Входные билеты не включены',
        remark: '● Встреча: Только указанные места в Куала-Лумпуре ● Бронирование: До 20:00 накануне отправления ● Время: Включает дорогу туда и обратно ● Питание: За свой счет ● Доплата: Свыше 10 часов & трансфер в аэропорт'
    }
};

// 快速添加函数 - Melaka Private Tour
function addMelakaTranslation(languageCode, autoSubmit = false) {
    const translation = melakaTranslations[languageCode];
    if (!translation) {
        console.error(`❌ 未找到语言代码 ${languageCode} 的翻译数据`);
        return false;
    }
    
    return addQRCodeTranslation(
        languageCode,
        translation.description,
        translation.remark,
        autoSubmit
    );
}

// 使用示例:
// addMelakaTranslation('ja');  // 添加日语翻译
// addMelakaTranslation('ko', true);  // 添加韩语翻译并自动提交

console.log('📚 QR Code翻译注入脚本已加载');
console.log('💡 使用方法: addMelakaTranslation("语言代码", 是否自动提交)');
console.log('🌐 支持语言: id, ja, ko, th, vi, ru');
