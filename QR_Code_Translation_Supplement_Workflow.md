# 🌐 QR Code翻译补充标准化工作流程

**版本**: v1.0  
**创建时间**: 2025-01-27  
**适用系统**: GoMyHire QR码管理系统  
**基于经验**: KTMB项目翻译补充实践  

---

## 📋 **工作流程概述**

本文档提供了一套标准化的QR Code项目翻译补充工作流程，适用于任何需要添加多语言翻译的QR Code项目。

### **标准10语言列表**
1. **English** (英语)
2. **简体中文** (Simplified Chinese)  
3. **繁體中文** (Traditional Chinese)
4. **Bahasa Melayu** (马来语)
5. **Bahasa Indonesia** (印尼语)
6. **日本語** (日语)
7. **한국어** (韩语)
8. **ภาษาไทย** (泰语)
9. **Tiếng Việt** (越南语)
10. **Русский** (俄语)

---

## 🔍 **阶段1: 项目分析与评估**

### **1.1 项目基础信息收集**
- [ ] 获取QR Code项目ID和名称
- [ ] 记录项目状态（启用/禁用）
- [ ] 统计子项目总数
- [ ] 识别服务类型分布

### **1.2 翻译状态评估**
```bash
# 系统操作步骤
1. 导航到: https://staging.gomyhire.com.my/s/qrCode
2. 点击目标项目的"Edit"按钮
3. 获取子项目列表
4. 逐一检查每个子项目的翻译状态
```

### **1.3 翻译缺口分析**
- [ ] 统计完整翻译服务数量 (10/10)
- [ ] 统计部分翻译服务数量 (1-9/10)
- [ ] 统计无翻译服务数量 (0/10)
- [ ] 计算总体翻译完整度百分比

### **1.4 优先级评估**
**高优先级服务特征:**
- 🎯 机场接送服务 (包含Airport、KLIA、Kuala Lumpur关键词)
- 🎯 云顶高原服务 (包含Genting关键词)
- 🎯 热门旅游服务 (Melaka、Kuala Selangor等)

---

## 📚 **阶段2: 翻译数据准备**

### **2.1 翻译内容来源**
1. **现有翻译库**: 检查`business-translation-tool.md`文件
2. **参考模板**: 使用同类服务的翻译作为模板
3. **新建翻译**: 为新服务类型创建翻译内容

### **2.2 翻译内容结构**
每个翻译条目包含两部分：
- **Description**: 服务描述 (50-100字)
- **Remark**: 注意事项和详细说明 (100-200字)

### **2.3 翻译质量标准**
- ✅ **准确性**: 内容与原文意思一致
- ✅ **完整性**: 包含所有关键信息
- ✅ **一致性**: 术语使用统一
- ✅ **本地化**: 符合目标语言表达习惯

---

## ⚙️ **阶段3: 系统操作执行**

### **3.1 单个翻译添加流程**
```bash
# 标准操作步骤
1. 点击子项目的"Translate"按钮
2. 点击"Add Translate"按钮
3. 选择目标语言 (Language下拉框)
4. 填入Description内容
5. 填入Remark内容
6. 点击"Submit"按钮保存
7. 验证翻译已成功添加
```

### **3.2 批量翻译操作策略**

#### **方法A: 逐语言添加 (推荐)**
- 为单个服务依次添加所有10种语言
- 优点: 集中处理，减少切换成本
- 适用: 翻译内容已准备完整的情况

#### **方法B: 逐服务添加**
- 为所有服务添加同一种语言，然后切换到下一种语言
- 优点: 语言切换少，翻译一致性好
- 适用: 多个服务使用相似翻译模板的情况

### **3.3 错误处理与重试**
- **元素不可见**: 刷新页面后重试
- **提交失败**: 检查必填字段是否完整
- **翻译重复**: 检查语言选择是否正确

---

## 📊 **阶段4: 质量控制与验证**

### **4.1 实时验证**
每添加一个翻译后立即验证：
- [ ] 翻译条目计数增加 (如: 1 to 1 of 1 entries)
- [ ] 翻译内容显示正确
- [ ] 语言标识准确

### **4.2 服务级验证**
每完成一个服务的所有翻译后：
- [ ] 翻译状态显示"10/10"
- [ ] 所有10种语言都已添加
- [ ] 翻译内容质量抽查

### **4.3 项目级验证**
完成整个项目后：
- [ ] 重新分析项目翻译状态
- [ ] 更新项目文档
- [ ] 生成完成报告

---

## ⏱️ **时间估算标准**

### **单个翻译条目**
- **数据准备**: 1-2分钟
- **系统录入**: 2-3分钟
- **验证确认**: 1分钟
- **小计**: 4-6分钟/条目

### **单个服务 (10种语言)**
- **已有翻译数据**: 30-40分钟
- **需要创建翻译**: 45-60分钟
- **包含验证时间**: +10分钟

### **项目级估算公式**
```
总时间 = (无翻译服务数 × 45分钟) + (部分翻译服务数 × 缺失语言数 × 4分钟) + 项目验证时间(15分钟)
```

---

## 📋 **工作清单模板**

### **项目开始前**
- [ ] 创建项目主文档: `{项目名称}_QR_Code_Complete_Report.md`
- [ ] 完成项目分析和翻译状态评估
- [ ] 准备所需翻译内容数据
- [ ] 制定翻译补充计划

### **翻译执行中**
- [ ] 按优先级顺序处理服务
- [ ] 每完成一个服务更新进展状态
- [ ] 记录遇到的问题和解决方案
- [ ] 定期保存工作进展

### **项目完成后**
- [ ] 验证所有服务翻译完整度
- [ ] 更新项目文档最终状态
- [ ] 生成项目完成总结
- [ ] 归档相关工作文件

---

## 🚨 **常见问题与解决方案**

### **问题1: 页面元素找不到**
**解决方案**: 刷新页面，等待完全加载后重试

### **问题2: 翻译提交失败**
**解决方案**: 检查Description和Remark是否都已填写

### **问题3: 语言选择错误**
**解决方案**: 删除错误翻译，重新添加正确语言

### **问题4: 翻译内容过长**
**解决方案**: 适当精简内容，保留核心信息

---

## 📈 **持续改进**

### **流程优化建议**
1. **收集反馈**: 记录每次使用流程的问题和建议
2. **更新标准**: 定期更新翻译质量标准和操作步骤
3. **工具改进**: 探索自动化工具减少手动操作
4. **培训材料**: 基于实际经验完善培训文档

### **版本控制**
- **v1.0**: 基于KTMB项目经验的初始版本
- **后续版本**: 根据更多项目实践持续优化

---

**文档维护**: 每完成一个QR Code项目翻译补充工作后，更新本流程文档  
**适用范围**: 所有GoMyHire QR码管理系统的翻译补充工作  
**更新频率**: 根据实际使用反馈定期更新
