# QR Code项目完整数据提取自动化方案

## 📋 **方案概述**

本方案基于Chrome MCP Server，设计了一套完整的QR Code项目数据提取自动化流程，能够系统性地提取单个QR Code项目的所有相关数据，包括主项目信息、子项目列表和翻译状态详情。

**目标系统**: GoMyHire QR码管理系统
**目标页面**: https://staging.gomyhire.com.my/s/qrCode
**核心工具**: Chrome MCP Server
**输出格式**: 结构化Markdown报告

---

## 🎯 **数据提取目标**

### 1. **QR Code主项目信息**
- **QR Code ID**: 从Edit按钮的onclick属性提取
- **QR Code名称**: 项目显示名称
- **项目状态**: 启用/禁用状态
- **创建时间**: 项目创建日期（如可获取）

### 2. **子项目完整列表**
- **sub_id**: 从Translate按钮的onclick属性提取
- **子项目名称**: 服务名称
- **子项目类型**: 自动分类（机场接送、云顶高原、票务服务、旅游服务、包车服务）
- **子项目序号**: 在列表中的位置

### 3. **翻译状态详情**
- **已有翻译语言数量**: 从#qrCodeSubTranslateTable_info提取
- **具体翻译语言列表**: 已翻译的语言名称
- **翻译完整度评估**: X/10格式
- **缺失语言识别**: 未翻译的语言列表
- **翻译质量状态**: 完整/部分/无翻译

---

## 🛠️ **Chrome MCP Server执行步骤 (基于HTML内容获取)**

### **步骤1: 定位目标QR Code项目**

使用`chrome_get_web_content_chrome-mcp-stdio`获取QR Code列表页面内容：

#### **1.1 获取QR Code项目列表**
```javascript
// 使用精确选择器获取QR Code项目表格
chrome_get_web_content_chrome-mcp-stdio({
    htmlContent: true,
    selector: "#qrCodeTable tbody"
})
```

#### **1.2 HTML解析逻辑**
从返回的HTML中解析目标QR Code项目信息：

```javascript
// HTML解析函数 (在本地执行)
function parseQrCodeList(htmlContent, targetId) {
    // 创建临时DOM解析器
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // 查找所有Edit按钮
    const editButtons = doc.querySelectorAll('button[onclick*="qrCodeMainModalOpen(\'Edit\',"]');

    for (let button of editButtons) {
        const onclick = button.getAttribute('onclick');
        const match = onclick.match(/qrCodeMainModalOpen\('Edit',\s*(\d+)\)/);

        if (match && match[1] === targetId) {
            const row = button.closest('tr');
            const nameCell = row.querySelector('td:first-child');
            const statusCell = row.querySelector('td:nth-child(3)');

            return {
                qrCodeId: match[1],
                qrCodeName: nameCell ? nameCell.textContent.trim() : 'Unknown',
                status: statusCell ? statusCell.textContent.trim() : 'Unknown',
                found: true
            };
        }
    }
    return { found: false, error: `未找到ID为${targetId}的QR Code项目` };
}
```

#### **1.3 打开主项目编辑窗口**
```javascript
// 使用chrome_click_element点击Edit按钮
chrome_click_element_chrome-mcp-stdio({
    selector: `button[onclick*="qrCodeMainModalOpen('Edit', ${targetId})"]`,
    waitForNavigation: false,
    timeout: 5000
})
```

### **步骤2: 提取子项目列表**

等待主项目编辑窗口加载后，使用`chrome_get_web_content_chrome-mcp-stdio`获取子项目数据：

#### **2.1 获取子项目表格内容**
```javascript
// 使用精确选择器获取子项目表格，避免内容截断
chrome_get_web_content_chrome-mcp-stdio({
    htmlContent: true,
    selector: "#qrCodeSubDataTable tbody"
})
```

#### **2.2 HTML解析逻辑**
从返回的HTML中解析所有子项目信息：

```javascript
// 子项目列表解析函数 (在本地执行)
function parseSubProjectList(htmlContent) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    const subProjects = [];
    const rows = doc.querySelectorAll('tr');

    rows.forEach((row, index) => {
        const nameCell = row.querySelector('td:first-child');
        const translateButton = row.querySelector('a[onclick*="qrCodeSubTranslate("]');

        if (nameCell && translateButton) {
            const onclick = translateButton.getAttribute('onclick');
            const match = onclick.match(/qrCodeSubTranslate\('(\d+)'\)/);

            if (match) {
                const subProjectName = nameCell.textContent.trim();
                const subId = match[1];

                // 自动分类服务类型
                const serviceType = classifyServiceType(subProjectName);

                subProjects.push({
                    序号: index + 1,
                    子项目名称: subProjectName,
                    sub_id: subId,
                    服务类型: serviceType
                });
            }
        }
    });

    return {
        success: true,
        count: subProjects.length,
        data: subProjects
    };
}

// 服务类型自动分类
function classifyServiceType(serviceName) {
    const name = serviceName.toLowerCase();

    if (name.includes('airport') || name.includes('klia') ||
        (name.includes('kuala lumpur') && (name.includes('transfer') || name.includes('pickup') || name.includes('dropoff')))) {
        return '🎯 机场接送';
    } else if (name.includes('genting') && (name.includes('transfer') || name.includes('charter'))) {
        return '🎯 云顶高原';
    } else if (name.includes('ticket') || name.includes('feeding') || name.includes('fireflies') ||
               name.includes('blue tear') || name.includes('sky mirror')) {
        return '票务服务';
    } else if (name.includes('tour') || name.includes('melaka') || name.includes('selangor')) {
        return '旅游服务';
    } else if (name.includes('charter') || name.includes('hourly')) {
        return '包车服务';
    } else {
        return '其他服务';
    }
}

// 执行提取
const allSubProjects = extractAllSubProjects();
```

### **步骤3: 递归检查翻译状态**

对每个子项目逐一检查翻译状态，使用分步骤的HTML内容获取方法：

#### **3.1 打开子项目翻译管理窗口**
```javascript
// 对每个子项目，点击其Translate按钮
chrome_click_element_chrome-mcp-stdio({
    selector: `a[onclick*="qrCodeSubTranslate('${subId}')"]`,
    waitForNavigation: false,
    timeout: 5000
})
```

#### **3.2 获取翻译状态信息**
使用两个精确的选择器分别获取翻译统计和语言列表：

```javascript
// 3.2.1 获取翻译条目统计信息
chrome_get_web_content_chrome-mcp-stdio({
    textContent: true,
    selector: "#qrCodeSubTranslateTable_info"
})

// 3.2.2 获取翻译语言列表 (使用精确选择器避免内容截断)
chrome_get_web_content_chrome-mcp-stdio({
    htmlContent: true,
    selector: "#qrCodeSubTranslateTable tbody"
})
```

#### **3.3 翻译状态解析逻辑**
```javascript
// 翻译状态解析函数 (在本地执行)
function parseTranslationStatus(infoText, tableHtml, subProject) {
    // 解析翻译条目数量
    let totalEntries = 0;
    if (infoText) {
        const match = infoText.match(/of (\d+) entries/);
        totalEntries = match ? parseInt(match[1]) : 0;
    }

    // 解析已有语言列表
    const existingLanguages = [];
    if (tableHtml) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(tableHtml, 'text/html');
        const rows = doc.querySelectorAll('tr');

        rows.forEach(row => {
            const langCell = row.querySelector('td:first-child');
            if (langCell) {
                existingLanguages.push(langCell.textContent.trim());
            }
        });
    }

    // 计算缺失语言
    const standardLanguages = ['English', '简体中文', '繁體中文', 'Bahasa Melayu', 'Bahasa Indonesia', '日本語', '한국어', 'ภาษาไทย', 'Tiếng Việt', 'Русский'];
    const missingLanguages = standardLanguages.filter(lang => !existingLanguages.includes(lang));

    // 评估翻译状态
    let translationStatus;
    if (totalEntries === 0) {
        translationStatus = '❌ **无翻译 (0/10)**';
    } else if (totalEntries === 10) {
        translationStatus = '✅ **完整翻译 (10/10)**';
    } else if (totalEntries === 1 && existingLanguages.includes('English')) {
        translationStatus = '⚠️ **仅英文 (1/10)**';
    } else {
        translationStatus = `⚠️ **部分翻译 (${totalEntries}/10)**`;
    }

    return {
        ...subProject,
        翻译状态: translationStatus,
        已有语言数量: totalEntries,
        已有语言列表: existingLanguages,
        缺失语言列表: missingLanguages,
        翻译完整度: `${totalEntries}/10`
    };
}
```

#### **3.4 关闭翻译管理窗口**
```javascript
// 使用键盘事件关闭模态窗口
chrome_keyboard_chrome-mcp-stdio({
    keys: "Escape"
})
```

---

## 📊 **标准输出格式**

### **QR Code项目基本信息表**
```markdown
| 项目属性 | 值 |
|----------|-----|
| **QR Code ID** | 81 |
| **项目名称** | KTMB |
| **项目状态** | ✅ 启用 |
| **子项目总数** | 6个 |
| **机场接送服务** | ❌ 不包含 |
```

### **子项目详细映射表**
```markdown
| 序号 | 子项目名称 | sub_id | 翻译状态 | 服务类型 |
|------|------------|--------|----------|----------|
| 1 | **Melaka Private Tour (10 Hours)** | **468** | ❌ **无翻译 (0/10)** | 旅游服务 |
| 2 | **Kuala Selangor Private Charter (6 Hours)** | **467** | ❌ **无翻译 (0/10)** | 旅游服务 |
| 3 | **Shuttle From Genting Highland** | **466** | ✅ **完整翻译 (10/10)** | 交通服务 |
| 4 | **Shuttle To Genting Highland** | **465** | ✅ **完整翻译 (10/10)** | 交通服务 |
| 5 | **Hourly Charter 3 Hour** | **464** | ❌ **无翻译 (0/10)** | 包车服务 |
| 6 | **Hourly Charter 8 Hour** | **463** | ✅ **完整翻译 (10/10)** | 包车服务 |
```

### **翻译状态汇总表**
```markdown
| 翻译状态 | 数量 | 占比 |
|----------|------|------|
| ✅ **完整翻译 (10/10)** | 3个 | 50.0% |
| ⚠️ **部分翻译 (1-9/10)** | 0个 | 0.0% |
| ❌ **无翻译 (0/10)** | 3个 | 50.0% |
```

### **机场接送服务专项分析表**
```markdown
| 序号 | 服务名称 | sub_id | 翻译状态 | 缺失语言数 |
|------|----------|--------|----------|------------|
| 1 | **Airport transfer (Klang Valley / Kuala Lumpur)** | **459** | ⚠️ **仅英文 (1/10)** | 9种 |
| 2 | **Airport transfer (Within Kuala Lumpur Area)** | **449** | ⚠️ **仅英文 (1/10)** | 9种 |
```

---

## 🔧 **错误处理与重试机制**

### **超时处理**
```javascript
function withTimeout(operation, timeoutMs = 10000) {
    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
            reject(new Error('操作超时'));
        }, timeoutMs);

        operation().then(result => {
            clearTimeout(timer);
            resolve(result);
        }).catch(error => {
            clearTimeout(timer);
            reject(error);
        });
    });
}
```

### **重试机制**
```javascript
async function retryOperation(operation, maxRetries = 3, delay = 2000) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            console.log(`操作失败，${delay}ms后进行第${i + 1}次重试...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
```

### **进度反馈**
```javascript
function createProgressTracker(total) {
    let current = 0;
    return {
        update: (message) => {
            current++;
            const percentage = ((current / total) * 100).toFixed(1);
            console.log(`[${current}/${total}] (${percentage}%) ${message}`);
        },
        complete: () => {
            console.log(`✅ 数据提取完成 (${total}/${total})`);
        }
    };
}
```

---

## 🚀 **完整执行示例**

### **Chrome MCP Server命令序列 (基于HTML内容获取)**

#### **阶段1: 页面导航与项目定位**
```javascript
// 1. 导航到目标页面
chrome_navigate_chrome-mcp-stdio({
    url: "https://staging.gomyhire.com.my/s/qrCode"
})

// 2. 获取QR Code项目列表
chrome_get_web_content_chrome-mcp-stdio({
    htmlContent: true,
    selector: "#qrCodeTable tbody"
})

// 3. 本地解析目标项目信息 (使用parseQrCodeList函数)

// 4. 点击目标项目的Edit按钮
chrome_click_element_chrome-mcp-stdio({
    selector: `button[onclick*="qrCodeMainModalOpen('Edit', ${targetId})"]`,
    waitForNavigation: false,
    timeout: 5000
})
```

#### **阶段2: 子项目数据提取**
```javascript
// 5. 获取子项目列表
chrome_get_web_content_chrome-mcp-stdio({
    htmlContent: true,
    selector: "#qrCodeSubDataTable tbody"
})

// 6. 本地解析子项目信息 (使用parseSubProjectList函数)
```

#### **阶段3: 翻译状态检查循环**
```javascript
// 对每个子项目重复以下步骤:

// 7.1 点击子项目Translate按钮
chrome_click_element_chrome-mcp-stdio({
    selector: `a[onclick*="qrCodeSubTranslate('${subId}')"]`,
    waitForNavigation: false,
    timeout: 5000
})

// 7.2 获取翻译统计信息
chrome_get_web_content_chrome-mcp-stdio({
    textContent: true,
    selector: "#qrCodeSubTranslateTable_info"
})

// 7.3 获取翻译语言列表
chrome_get_web_content_chrome-mcp-stdio({
    htmlContent: true,
    selector: "#qrCodeSubTranslateTable tbody"
})

// 7.4 本地解析翻译状态 (使用parseTranslationStatus函数)

// 7.5 关闭翻译管理窗口
chrome_keyboard_chrome-mcp-stdio({
    keys: "Escape"
})
```

#### **阶段4: 报告生成**
```javascript
// 8. 基于收集的结构化数据生成Markdown报告
// 使用本地JavaScript函数处理所有数据解析和报告生成
```

---

**方案创建时间**: 2025-01-27
**适用系统**: GoMyHire QR码管理系统
**技术栈**: Chrome MCP Server + JavaScript注入
**输出格式**: 结构化Markdown报告
**执行效率**: 单个项目约2-5分钟完成